import React, { useState, useEffect } from 'react';
import {
  Row,
  Col,
  Card,
  Button,
  Space,
  Typography,
  Tag,
  Rate,
  Divider,
  Image,
  InputNumber,
  Breadcrumb,
  Tabs,
  List,
  Avatar,
  Spin,
  message,
  Modal,
  Tooltip,
  Progress,
  Statistic
} from 'antd';
import {
  ShoppingCartOutlined,
  HeartOutlined,
  ShareAltOutlined,
  EyeOutlined,
  UserOutlined,
  CalendarOutlined,
  BookOutlined,
  HomeOutlined,
  StarFilled,
  MessageOutlined,
  LikeOutlined,
  DislikeOutlined,
  MoreOutlined
} from '@ant-design/icons';
import styled from 'styled-components';
import { useParams, useNavigate } from 'react-router-dom';
import { useCartStore } from '../../stores/cartStore';
import { useAuthStore } from '../../stores/authStore';
import ReviewSystem from '../../components/business/ReviewSystem';
import EnhancedBookCard from '../../components/business/EnhancedBookCard';
import { booksService } from '../../services/books';
import { favoritesService } from '../../services/favorites';
import api from '../../services/api';

const { Title, Text, Paragraph } = Typography;
const { TabPane } = Tabs;

const BookDetailContainer = styled.div`
  min-height: 100vh;
  background: #f5f5f5;
  
  .page-header {
    background: white;
    padding: 16px 0;
    border-bottom: 1px solid #f0f0f0;
    
    .header-content {
      max-width: 1200px;
      margin: 0 auto;
      padding: 0 24px;
    }
  }
  
  .book-detail-content {
    max-width: 1200px;
    margin: 0 auto;
    padding: 24px;
    
    .main-content {
      background: white;
      border-radius: 12px;
      overflow: hidden;
      box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
      
      .book-info-section {
        padding: 32px;
        
        .book-images {
          .main-image {
            border-radius: 12px;
            overflow: hidden;
            box-shadow: 0 4px 16px rgba(0, 0, 0, 0.1);
            
            .ant-image {
              width: 100%;
              height: 400px;
              
              img {
                width: 100%;
                height: 100%;
                object-fit: cover;
              }
            }
          }
          
          .thumbnail-list {
            margin-top: 16px;
            display: flex;
            gap: 8px;
            
            .thumbnail-item {
              width: 60px;
              height: 75px;
              border-radius: 6px;
              overflow: hidden;
              cursor: pointer;
              border: 2px solid transparent;
              transition: all 0.3s ease;
              
              &:hover,
              &.active {
                border-color: #1677ff;
              }
              
              img {
                width: 100%;
                height: 100%;
                object-fit: cover;
              }
            }
          }
        }
        
        .book-details {
          .book-title {
            font-size: 28px;
            font-weight: 700;
            margin-bottom: 12px;
            line-height: 1.3;
          }
          
          .book-subtitle {
            font-size: 16px;
            color: #8c8c8c;
            margin-bottom: 16px;
          }
          
          .book-rating {
            display: flex;
            align-items: center;
            gap: 12px;
            margin-bottom: 20px;
            
            .rating-stars {
              display: flex;
              align-items: center;
              gap: 8px;
            }
            
            .rating-text {
              color: #8c8c8c;
              font-size: 14px;
            }
          }
          
          .book-price {
            margin-bottom: 24px;
            
            .current-price {
              font-size: 32px;
              font-weight: 700;
              color: #ff4d4f;
              margin-right: 16px;
            }
            
            .original-price {
              font-size: 18px;
              color: #8c8c8c;
              text-decoration: line-through;
              margin-right: 12px;
            }
            
            .discount-tag {
              font-size: 12px;
            }
          }
          
          .book-meta {
            margin-bottom: 24px;
            
            .meta-item {
              display: flex;
              align-items: center;
              margin-bottom: 8px;
              
              .meta-label {
                width: 80px;
                color: #8c8c8c;
                font-size: 14px;
              }
              
              .meta-value {
                flex: 1;
                font-size: 14px;
              }
            }
          }
          
          .book-tags {
            margin-bottom: 24px;
            
            .ant-tag {
              margin-bottom: 8px;
            }
          }
          
          .purchase-section {
            background: #fafafa;
            padding: 24px;
            border-radius: 12px;
            
            .quantity-selector {
              margin-bottom: 20px;
              
              .quantity-label {
                margin-bottom: 8px;
                font-weight: 600;
              }
              
              .quantity-input {
                width: 120px;
              }
              
              .stock-info {
                margin-left: 12px;
                color: #8c8c8c;
                font-size: 14px;
              }
            }
            
            .action-buttons {
              display: flex;
              gap: 12px;
              
              .add-cart-btn {
                flex: 1;
                height: 48px;
                font-size: 16px;
                font-weight: 600;
                border-radius: 8px;
              }
              
              .buy-now-btn {
                flex: 1;
                height: 48px;
                font-size: 16px;
                font-weight: 600;
                border-radius: 8px;
                background: linear-gradient(135deg, #ff4d4f, #ff7875);
                border: none;
                
                &:hover {
                  background: linear-gradient(135deg, #d9363e, #ff4d4f);
                  transform: translateY(-1px);
                  box-shadow: 0 4px 12px rgba(255, 77, 79, 0.3);
                }
              }
              
              .favorite-btn {
                width: 48px;
                height: 48px;
                border-radius: 8px;
                display: flex;
                align-items: center;
                justify-content: center;
                
                &.favorited {
                  color: #ff4d4f;
                  border-color: #ff4d4f;
                }
              }
              
              .share-btn {
                width: 48px;
                height: 48px;
                border-radius: 8px;
                display: flex;
                align-items: center;
                justify-content: center;
              }
            }
            
            .seller-info {
              margin-top: 20px;
              padding-top: 20px;
              border-top: 1px solid #f0f0f0;
              
              .seller-header {
                display: flex;
                align-items: center;
                gap: 12px;
                margin-bottom: 12px;
                
                .seller-avatar {
                  width: 40px;
                  height: 40px;
                }
                
                .seller-details {
                  flex: 1;
                  
                  .seller-name {
                    font-weight: 600;
                    margin-bottom: 4px;
                  }
                  
                  .seller-rating {
                    display: flex;
                    align-items: center;
                    gap: 8px;
                    font-size: 12px;
                    color: #8c8c8c;
                  }
                }
                
                .contact-seller-btn {
                  border-radius: 16px;
                  height: 32px;
                  padding: 0 16px;
                  font-size: 12px;
                }
              }
            }
          }
        }
      }
      
      .book-tabs-section {
        .ant-tabs {
          .ant-tabs-nav {
            padding: 0 32px;
            margin: 0;
            
            .ant-tabs-tab {
              padding: 16px 24px;
              font-size: 16px;
              font-weight: 600;
            }
          }
          
          .ant-tabs-content {
            padding: 32px;
          }
        }
        
        .description-content {
          .description-text {
            line-height: 1.8;
            font-size: 15px;
            color: #262626;
          }
          
          .book-specs {
            margin-top: 24px;
            
            .specs-title {
              font-size: 16px;
              font-weight: 600;
              margin-bottom: 16px;
            }
            
            .specs-table {
              .spec-row {
                display: flex;
                padding: 12px 0;
                border-bottom: 1px solid #f0f0f0;
                
                &:last-child {
                  border-bottom: none;
                }
                
                .spec-label {
                  width: 120px;
                  color: #8c8c8c;
                  font-size: 14px;
                }
                
                .spec-value {
                  flex: 1;
                  font-size: 14px;
                }
              }
            }
          }
        }
        
        .reviews-content {
          .reviews-summary {
            background: #fafafa;
            padding: 24px;
            border-radius: 12px;
            margin-bottom: 24px;
            
            .summary-stats {
              display: flex;
              align-items: center;
              gap: 32px;
              
              .overall-rating {
                text-align: center;
                
                .rating-score {
                  font-size: 48px;
                  font-weight: 700;
                  color: #faad14;
                  margin-bottom: 8px;
                }
                
                .rating-stars {
                  margin-bottom: 8px;
                }
                
                .rating-count {
                  color: #8c8c8c;
                  font-size: 14px;
                }
              }
              
              .rating-breakdown {
                flex: 1;
                
                .breakdown-item {
                  display: flex;
                  align-items: center;
                  gap: 12px;
                  margin-bottom: 8px;
                  
                  .star-label {
                    width: 60px;
                    font-size: 14px;
                  }
                  
                  .progress-bar {
                    flex: 1;
                  }
                  
                  .count-label {
                    width: 40px;
                    text-align: right;
                    font-size: 14px;
                    color: #8c8c8c;
                  }
                }
              }
            }
          }
        }
      }
    }
    
    .recommendations-section {
      margin-top: 24px;
      background: white;
      border-radius: 12px;
      padding: 24px;
      box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
      
      .section-title {
        font-size: 20px;
        font-weight: 700;
        margin-bottom: 20px;
      }
      
      .recommendations-grid {
        display: grid;
        grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
        gap: 16px;
      }
    }
  }
`;

interface BookDetailPageProps {}

const BookDetailPage: React.FC<BookDetailPageProps> = () => {
  const { id } = useParams<{ id: string }>();
  const navigate = useNavigate();
  const { addItem } = useCartStore();
  const { isAuthenticated, user } = useAuthStore();
  
  const [loading, setLoading] = useState(true);
  const [book, setBook] = useState<any>(null);
  const [quantity, setQuantity] = useState(1);
  const [isFavorited, setIsFavorited] = useState(false);
  const [activeImageIndex, setActiveImageIndex] = useState(0);
  const [recommendedBooks, setRecommendedBooks] = useState<any[]>([]);

  useEffect(() => {
    if (id) {
      loadBookDetail();
      loadRecommendedBooks();
    }
  }, [id]);

  const loadBookDetail = async () => {
    try {
      setLoading(true);
      const response = await booksService.getBook(id!);
      
      if (response.success) {
        setBook(response.data);
        // 检查是否已收藏
        if (isAuthenticated) {
          checkFavoriteStatus();
        }
      } else {
        message.error('图书不存在');
        navigate('/books');
      }
    } catch (error) {
      message.error('加载图书详情失败');
      navigate('/books');
    } finally {
      setLoading(false);
    }
  };

  const loadRecommendedBooks = async () => {
    try {
      const response = await api.get(`/recommendations/similar/${id}`, {
        params: { limit: 8 }
      });

      if (response.data.success) {
        setRecommendedBooks(response.data.data.books);
      }
    } catch (error) {
      console.error('加载推荐图书失败:', error);
    }
  };

  const checkFavoriteStatus = async () => {
    try {
      const response = await favoritesService.checkFavorite(id!);
      if (response.success) {
        setIsFavorited(response.data.is_favorited);
      }
    } catch (error) {
      console.error('检查收藏状态失败:', error);
    }
  };

  const handleAddToCart = () => {
    if (!book) return;
    
    addItem({
      id: book.id,
      title: book.title,
      price: book.price,
      cover_image: book.cover_image,
      stock: book.stock,
      quantity
    });
    
    message.success('已添加到购物车');
  };

  const handleBuyNow = () => {
    if (!isAuthenticated) {
      message.warning('请先登录');
      navigate('/login');
      return;
    }
    
    handleAddToCart();
    navigate('/cart');
  };

  const handleToggleFavorite = async () => {
    if (!isAuthenticated) {
      message.warning('请先登录');
      navigate('/login');
      return;
    }

    try {
      if (isFavorited) {
        await favoritesService.removeFavorite(id!);
        setIsFavorited(false);
        message.success('已取消收藏');
      } else {
        await favoritesService.addFavorite(id!);
        setIsFavorited(true);
        message.success('已添加到收藏');
      }
    } catch (error) {
      message.error('操作失败');
    }
  };

  const handleShare = () => {
    if (navigator.share) {
      navigator.share({
        title: book?.title,
        text: book?.description,
        url: window.location.href
      });
    } else {
      // 复制链接到剪贴板
      navigator.clipboard.writeText(window.location.href);
      message.success('链接已复制到剪贴板');
    }
  };

  if (loading) {
    return (
      <BookDetailContainer>
        <div style={{ 
          display: 'flex', 
          justifyContent: 'center', 
          alignItems: 'center', 
          height: '50vh' 
        }}>
          <Spin size="large" />
        </div>
      </BookDetailContainer>
    );
  }

  if (!book) {
    return (
      <BookDetailContainer>
        <div style={{ 
          display: 'flex', 
          justifyContent: 'center', 
          alignItems: 'center', 
          height: '50vh',
          flexDirection: 'column'
        }}>
          <BookOutlined style={{ fontSize: 64, color: '#d9d9d9', marginBottom: 16 }} />
          <Title level={3}>图书不存在</Title>
          <Button type="primary" onClick={() => navigate('/books')}>
            返回图书列表
          </Button>
        </div>
      </BookDetailContainer>
    );
  }

  const images = book.images || [book.cover_image].filter(Boolean);
  const specs = [
    { label: 'ISBN', value: book.isbn },
    { label: '作者', value: book.author },
    { label: '出版社', value: book.publisher },
    { label: '出版时间', value: book.publish_date },
    { label: '页数', value: book.pages },
    { label: '装帧', value: book.binding },
    { label: '开本', value: book.format },
    { label: '语言', value: book.language || '中文' }
  ].filter(spec => spec.value);

  return (
    <BookDetailContainer>
      {/* 页面头部 */}
      <div className="page-header">
        <div className="header-content">
          <Breadcrumb>
            <Breadcrumb.Item>
              <HomeOutlined />
              <span onClick={() => navigate('/')} style={{ cursor: 'pointer', marginLeft: 8 }}>
                首页
              </span>
            </Breadcrumb.Item>
            <Breadcrumb.Item>
              <BookOutlined />
              <span onClick={() => navigate('/books')} style={{ cursor: 'pointer', marginLeft: 8 }}>
                图书列表
              </span>
            </Breadcrumb.Item>
            <Breadcrumb.Item>{book.title}</Breadcrumb.Item>
          </Breadcrumb>
        </div>
      </div>

      <div className="book-detail-content">
        {/* 主要内容 */}
        <Card className="main-content">
          {/* 图书信息区域 */}
          <div className="book-info-section">
            <Row gutter={48}>
              {/* 图书图片 */}
              <Col xs={24} md={10}>
                <div className="book-images">
                  <div className="main-image">
                    <Image
                      src={images[activeImageIndex] || '/images/book-placeholder.png'}
                      alt={book.title}
                      preview={{
                        src: images[activeImageIndex]
                      }}
                    />
                  </div>
                  
                  {images.length > 1 && (
                    <div className="thumbnail-list">
                      {images.map((image: string, index: number) => (
                        <div
                          key={index}
                          className={`thumbnail-item ${index === activeImageIndex ? 'active' : ''}`}
                          onClick={() => setActiveImageIndex(index)}
                        >
                          <img src={image} alt={`${book.title} ${index + 1}`} />
                        </div>
                      ))}
                    </div>
                  )}
                </div>
              </Col>

              {/* 图书详情 */}
              <Col xs={24} md={14}>
                <div className="book-details">
                  <Title level={1} className="book-title">{book.title}</Title>
                  {book.subtitle && (
                    <div className="book-subtitle">{book.subtitle}</div>
                  )}
                  
                  <div className="book-rating">
                    <div className="rating-stars">
                      <Rate disabled value={book.rating || 0} />
                      <span className="rating-text">
                        {book.rating?.toFixed(1) || '暂无评分'} ({book.review_count || 0}条评价)
                      </span>
                    </div>
                  </div>
                  
                  <div className="book-price">
                    <span className="current-price">¥{book.price?.toFixed(2)}</span>
                    {book.original_price && book.original_price > book.price && (
                      <>
                        <span className="original-price">¥{book.original_price.toFixed(2)}</span>
                        <Tag color="red" className="discount-tag">
                          {Math.round((1 - book.price / book.original_price) * 100)}折
                        </Tag>
                      </>
                    )}
                  </div>
                  
                  <div className="book-meta">
                    {book.author && (
                      <div className="meta-item">
                        <span className="meta-label">作者:</span>
                        <span className="meta-value">{book.author}</span>
                      </div>
                    )}
                    {book.publisher && (
                      <div className="meta-item">
                        <span className="meta-label">出版社:</span>
                        <span className="meta-value">{book.publisher}</span>
                      </div>
                    )}
                    {book.condition && (
                      <div className="meta-item">
                        <span className="meta-label">品相:</span>
                        <span className="meta-value">
                          <Tag color="green">{book.condition}</Tag>
                        </span>
                      </div>
                    )}
                    <div className="meta-item">
                      <span className="meta-label">库存:</span>
                      <span className="meta-value">
                        {book.stock > 0 ? `${book.stock} 本` : '暂无库存'}
                      </span>
                    </div>
                  </div>
                  
                  {book.tags && book.tags.length > 0 && (
                    <div className="book-tags">
                      {book.tags.map((tag: string, index: number) => (
                        <Tag key={index} color="blue">{tag}</Tag>
                      ))}
                    </div>
                  )}
                  
                  {/* 购买区域 */}
                  <div className="purchase-section">
                    <div className="quantity-selector">
                      <div className="quantity-label">数量:</div>
                      <Space>
                        <InputNumber
                          className="quantity-input"
                          min={1}
                          max={book.stock}
                          value={quantity}
                          onChange={(value) => setQuantity(value || 1)}
                        />
                        <span className="stock-info">库存 {book.stock} 本</span>
                      </Space>
                    </div>
                    
                    <div className="action-buttons">
                      <Button
                        type="primary"
                        className="add-cart-btn"
                        icon={<ShoppingCartOutlined />}
                        onClick={handleAddToCart}
                        disabled={book.stock === 0}
                      >
                        加入购物车
                      </Button>
                      
                      <Button
                        className="buy-now-btn"
                        onClick={handleBuyNow}
                        disabled={book.stock === 0}
                      >
                        立即购买
                      </Button>
                      
                      <Tooltip title={isFavorited ? '取消收藏' : '收藏'}>
                        <Button
                          className={`favorite-btn ${isFavorited ? 'favorited' : ''}`}
                          icon={<HeartOutlined />}
                          onClick={handleToggleFavorite}
                        />
                      </Tooltip>
                      
                      <Tooltip title="分享">
                        <Button
                          className="share-btn"
                          icon={<ShareAltOutlined />}
                          onClick={handleShare}
                        />
                      </Tooltip>
                    </div>
                    
                    {/* 卖家信息 */}
                    {book.seller && (
                      <div className="seller-info">
                        <div className="seller-header">
                          <Avatar 
                            className="seller-avatar"
                            src={book.seller.avatar}
                            icon={<UserOutlined />}
                          />
                          <div className="seller-details">
                            <div className="seller-name">{book.seller.username}</div>
                            <div className="seller-rating">
                              <Rate disabled value={book.seller.rating || 0} size="small" />
                              <span>({book.seller.review_count || 0}条评价)</span>
                            </div>
                          </div>
                          <Button 
                            className="contact-seller-btn"
                            icon={<MessageOutlined />}
                          >
                            联系卖家
                          </Button>
                        </div>
                      </div>
                    )}
                  </div>
                </div>
              </Col>
            </Row>
          </div>

          {/* 详情标签页 */}
          <div className="book-tabs-section">
            <Tabs defaultActiveKey="description">
              <TabPane tab="图书详情" key="description">
                <div className="description-content">
                  <Paragraph className="description-text">
                    {book.description || '暂无详细描述'}
                  </Paragraph>
                  
                  {specs.length > 0 && (
                    <div className="book-specs">
                      <Title level={4} className="specs-title">图书规格</Title>
                      <div className="specs-table">
                        {specs.map((spec, index) => (
                          <div key={index} className="spec-row">
                            <div className="spec-label">{spec.label}:</div>
                            <div className="spec-value">{spec.value}</div>
                          </div>
                        ))}
                      </div>
                    </div>
                  )}
                </div>
              </TabPane>
              
              <TabPane tab={`评价 (${book.review_count || 0})`} key="reviews">
                <div className="reviews-content">
                  {book.review_count > 0 && (
                    <div className="reviews-summary">
                      <div className="summary-stats">
                        <div className="overall-rating">
                          <div className="rating-score">{book.rating?.toFixed(1) || '0.0'}</div>
                          <div className="rating-stars">
                            <Rate disabled value={book.rating || 0} />
                          </div>
                          <div className="rating-count">{book.review_count} 条评价</div>
                        </div>
                        
                        <div className="rating-breakdown">
                          {[5, 4, 3, 2, 1].map(star => (
                            <div key={star} className="breakdown-item">
                              <div className="star-label">{star}星</div>
                              <Progress 
                                className="progress-bar"
                                percent={Math.random() * 100} // 模拟数据
                                showInfo={false}
                                strokeColor="#faad14"
                              />
                              <div className="count-label">{Math.floor(Math.random() * 50)}</div>
                            </div>
                          ))}
                        </div>
                      </div>
                    </div>
                  )}
                  
                  <ReviewSystem bookId={book.id} />
                </div>
              </TabPane>
            </Tabs>
          </div>
        </Card>

        {/* 推荐图书 */}
        {recommendedBooks.length > 0 && (
          <div className="recommendations-section">
            <Title level={3} className="section-title">相关推荐</Title>
            <div className="recommendations-grid">
              {recommendedBooks.map(recommendedBook => (
                <EnhancedBookCard 
                  key={recommendedBook.id} 
                  book={recommendedBook}
                  size="small"
                />
              ))}
            </div>
          </div>
        )}
      </div>
    </BookDetailContainer>
  );
};

export default BookDetailPage;
